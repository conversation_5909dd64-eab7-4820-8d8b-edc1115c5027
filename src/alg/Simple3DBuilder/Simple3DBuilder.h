#ifndef SIMPLE_3D_BUILDER_H
#define SIMPLE_3D_BUILDER_H
#include <string>

/**
 * @class Simple3DBuilder
 * @brief 通用三维模型创建工具
 */
class Simple3DBuilder {
 public:
  // 网格化参数常量 - 使用更精细的参数确保小细节被正确捕获
  static constexpr double MESH_LINEAR_DEFLECTION = 0.01;  // 线性偏差
  static constexpr double MESH_ANGULAR_DEFLECTION = 10;    // 角度偏差

  /**
   * @brief 创建开口箱子三维模型
   *
   * @param inner_length  内框长度（单位：毫米）
   * @param inner_width   内框宽度（单位：毫米）
   * @param inner_height  内框高度（单位：毫米）
   * @param outer_length  外框长度（单位：毫米）
   * @param outer_width   外框宽度（单位：毫米）
   * @param outer_height  外框高度（单位：毫米）
   * @param corner_radius 倒角半径（单位：毫米）, 0: 不使用倒角
   * @param cloud_points  PLY点云数量(STL模型采样点个数)
   * @param output_dir    模型文件保存文件夹
   * @param output_stl    是否输出STL模型文件，默认: false
   * @return 模型文件路径
   */
  static std::string generate_openbox(
      double inner_length,
      double inner_width,
      double inner_height,
      double outer_length,
      double outer_width,
      double outer_height,
      double corner_radius,
      size_t cloud_points,
      const std::string& output_dir,
      bool output_stl = false);

  /**
   * @brief STL模型文件转PLY点云文件（随机采样）
   *
   * @param inner_length  内框长度（单位：毫米）
   * @param inner_width   内框宽度（单位：毫米）
   * @param inner_height  内框高度（单位：毫米）
   * @param outer_length  外框长度（单位：毫米）
   * @param outer_width   外框宽度（单位：毫米）
   * @param outer_height  外框高度（单位：毫米）
   * @param corner_radius 倒角半径（单位：毫米）
   * @param cloud_points  PLY点云数量(STL模型采样点个数)
   * @param output_dir    模型文件保存文件夹
   * @return 模型文件路径
   */
  static void sample_stl_to_ply(const std::string& stl_path, const std::string& ply_path, int target_total_points);

  // 新增函数：生成机器人末端执行工具
  static std::string generate_robot_tool(
        int tool_type, double tool_diameter, double tool_length, double tip_length,
        int flange_type, double flange_diameter, double flange_height, double bolt_circle, int bolt_number,
        size_t cloud_points, const std::string& output_dir, bool output_stl);

};

#endif  // SIMPLE_3D_BUILDER_H